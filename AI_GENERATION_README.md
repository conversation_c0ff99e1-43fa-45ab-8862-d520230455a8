# AI婚纱照生成服务 - 使用指南

## 概述

本项目已优化AI生成服务，使用laozhang.ai API来生成高质量的婚纱照。服务包含图片分析、prompt优化和多风格生成功能。

## 主要优化内容

### 1. API配置更新
- ✅ 更新API基础URL为 `https://api.laozhang.ai/v1`
- ✅ 优化请求头和超时配置
- ✅ 添加详细的错误处理和日志记录
- ✅ 创建统一的配置管理 (`AIConfig`)

### 2. 婚纱照Prompt优化
- ✅ 专业级的图片分析prompt，包含详细的人物特征分析
- ✅ 多角度综合分析支持（最多5张图片）
- ✅ 增强的DALL-E生成prompt，确保高质量输出
- ✅ 预设的婚纱照风格和场景配置

### 3. 功能增强
- ✅ 多风格生成支持（vivid/natural）
- ✅ 灵活的参数配置（尺寸、质量、风格）
- ✅ 文件验证和大小限制
- ✅ 详细的生成结果数据结构

## 快速开始

### 1. 配置API密钥

在 `lib/core/config/ai_config.dart` 中设置您的API密钥：

```dart
class AIConfig {
  static const String apiKey = 'YOUR_ACTUAL_API_KEY'; // 替换为真实的API密钥
  // ...
}
```

### 2. 基本使用

```dart
import 'package:your_app/core/services/ai_generation_service.dart';

final aiService = AIGenerationService();

// 快速生成单张婚纱照
final result = await aiService.quickGenerateWeddingPhoto(imageFile);

if (result.success) {
  print('生成成功: ${result.generatedImageUrl}');
  print('分析结果: ${result.analysis}');
} else {
  print('生成失败: ${result.message}');
}
```

### 3. 高级功能

```dart
// 多张图片高级生成（多风格）
final result = await aiService.premiumGenerateWeddingPhotos([
  File('image1.jpg'),
  File('image2.jpg'),
]);

// 自定义参数生成
final result = await aiService.generateWeddingPhotos(
  [imageFile],
  style: 'natural',
  size: '1024x1024',
  quality: 'hd',
  generateMultipleStyles: true,
);

// 仅分析图片
final analysis = await aiService.analyzeImageForWeddingPhoto(imageFile);
```

## API调用示例

基于您提供的API调用示例，服务现在使用以下配置：

```javascript
// 参考的API调用方式
const openai = new OpenAI({
  apiKey: "YOUR_API_KEY",
  baseURL: "https://api.laozhang.ai/v1"
});

const completion = await openai.chat.completions.create({
  model: "gpt-4o",
  messages: [
    {"role": "user", "content": "write a haiku about ai"}
  ]
});
```

对应的Dart实现：

```dart
final requestData = {
  'model': 'gpt-4o',
  'messages': [
    {
      'role': 'user',
      'content': [
        {'type': 'text', 'text': prompt},
        {'type': 'image_url', 'image_url': {'url': 'data:image/jpeg;base64,$base64Image'}}
      ]
    }
  ],
  'max_tokens': 1200,
  'temperature': 0.7,
};
```

## 婚纱照风格预设

服务提供多种预设风格：

- **经典优雅** (`classic`): 传统经典的婚纱照风格
- **浪漫梦幻** (`romantic`): 充满浪漫气息的梦幻婚纱照
- **现代时尚** (`modern`): 现代简约的时尚婚纱照风格
- **复古怀旧** (`vintage`): 复古风格的怀旧婚纱照
- **自然清新** (`natural`): 自然清新的户外婚纱照风格

## 场景预设

- **教堂** (`church`): 庄严神圣的教堂场景
- **花园** (`garden`): 美丽的花园场景
- **海滩** (`beach`): 浪漫的海滩场景
- **城堡** (`castle`): 童话般的城堡场景
- **摄影棚** (`studio`): 专业的室内摄影棚

## 配置参数

### 图片限制
- 单张图片最大: 10MB
- 总图片大小: 50MB
- 最大图片数量: 5张
- 支持格式: jpg, jpeg, png, webp, gif

### 生成参数
- 尺寸选项: 512x512, 1024x1024, 1792x1024
- 质量选项: standard, hd
- 风格选项: vivid, natural

### 超时设置
- 连接超时: 30秒
- 接收超时: 120秒
- 发送超时: 60秒

## 错误处理

服务提供详细的错误处理：

```dart
try {
  final result = await aiService.generateWeddingPhotos(images);
  // 处理成功结果
} on DioException catch (e) {
  // 网络错误
  print('网络错误: ${e.message}');
} catch (e) {
  // 其他错误
  print('生成失败: $e');
}
```

## 生成结果数据结构

```dart
class GenerationResult {
  final bool success;                    // 是否成功
  final String analysis;                 // AI分析结果
  final String generatedImageUrl;        // 主要生成的图片URL
  final List<String> additionalImageUrls; // 额外生成的图片URLs
  final String message;                  // 结果消息
  final DateTime generationTime;         // 生成时间
  final String style;                    // 使用的风格
  final String size;                     // 图片尺寸
  final String quality;                  // 图片质量
  
  // 便捷方法
  List<String> get allImageUrls;         // 所有图片URLs
  bool get hasMultipleImages;            // 是否有多张图片
  int get imageCount;                    // 图片总数
}
```

## 示例代码

完整的使用示例请参考 `lib/examples/ai_generation_example.dart` 文件。

## 注意事项

1. **API密钥**: 请确保在 `AIConfig` 中设置正确的API密钥
2. **网络连接**: 确保设备有稳定的网络连接
3. **图片格式**: 仅支持常见的图片格式
4. **文件大小**: 注意图片文件大小限制
5. **API限制**: 遵守API提供商的使用限制和频率限制

## 调试

启用调试模式查看详细日志：

```dart
// 在debug模式下会自动启用详细日志
if (kDebugMode) {
  print('调试信息: ${AIConfig.getDebugInfo()}');
}
```

## 更新日志

- ✅ 更新API基础URL为laozhang.ai
- ✅ 优化婚纱照生成prompt
- ✅ 添加多风格生成支持
- ✅ 增强错误处理和日志记录
- ✅ 创建统一配置管理
- ✅ 添加文件验证功能
- ✅ 提供完整的使用示例

## 技术支持

如有问题，请检查：
1. API密钥是否正确设置
2. 网络连接是否正常
3. 图片文件是否符合要求
4. 查看控制台日志获取详细错误信息
