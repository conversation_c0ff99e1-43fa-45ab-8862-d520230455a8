/// AI服务配置
class AIConfig {
  /// AI API基础URL
  static const String baseUrl = 'https://api.laozhang.ai/v1';
  
  /// API密钥 - 请在实际使用时替换为真实的API密钥
  static const String apiKey = 'YOUR_API_KEY';
  
  /// 默认模型配置
  static const String defaultChatModel = 'gpt-4o';
  static const String defaultImageModel = 'dall-e-3';
  
  /// 请求超时配置
  static const Duration connectTimeout = Duration(seconds: 30);
  static const Duration receiveTimeout = Duration(seconds: 120);
  static const Duration sendTimeout = Duration(seconds: 60);
  
  /// 图片生成配置
  static const Map<String, String> imageSizes = {
    'small': '512x512',
    'medium': '1024x1024',
    'large': '1792x1024',
  };
  
  static const Map<String, String> imageQualities = {
    'standard': 'standard',
    'hd': 'hd',
  };
  
  static const Map<String, String> imageStyles = {
    'vivid': 'vivid',
    'natural': 'natural',
  };
  
  /// 文件大小限制
  static const int maxSingleImageSize = 10 * 1024 * 1024; // 10MB
  static const int maxTotalImageSize = 50 * 1024 * 1024; // 50MB
  static const int maxImageCount = 5;
  
  /// 支持的图片格式
  static const List<String> supportedImageFormats = [
    'jpg', 'jpeg', 'png', 'webp', 'gif'
  ];
  
  /// Token限制
  static const int maxAnalysisTokens = 1200;
  static const int maxGenerationTokens = 4000;
  
  /// 重试配置
  static const int maxRetryAttempts = 3;
  static const Duration retryDelay = Duration(seconds: 2);
  
  /// 婚纱照风格预设
  static const Map<String, Map<String, dynamic>> weddingStyles = {
    'classic': {
      'name': '经典优雅',
      'description': '传统经典的婚纱照风格，优雅大方',
      'keywords': ['elegant', 'classic', 'timeless', 'sophisticated'],
      'style': 'natural',
      'quality': 'hd',
    },
    'romantic': {
      'name': '浪漫梦幻',
      'description': '充满浪漫气息的梦幻婚纱照',
      'keywords': ['romantic', 'dreamy', 'soft', 'ethereal'],
      'style': 'vivid',
      'quality': 'hd',
    },
    'modern': {
      'name': '现代时尚',
      'description': '现代简约的时尚婚纱照风格',
      'keywords': ['modern', 'fashion', 'minimalist', 'contemporary'],
      'style': 'vivid',
      'quality': 'hd',
    },
    'vintage': {
      'name': '复古怀旧',
      'description': '复古风格的怀旧婚纱照',
      'keywords': ['vintage', 'retro', 'nostalgic', 'classic'],
      'style': 'natural',
      'quality': 'hd',
    },
    'natural': {
      'name': '自然清新',
      'description': '自然清新的户外婚纱照风格',
      'keywords': ['natural', 'fresh', 'outdoor', 'organic'],
      'style': 'natural',
      'quality': 'hd',
    },
  };
  
  /// 场景预设
  static const Map<String, Map<String, dynamic>> weddingScenes = {
    'church': {
      'name': '教堂',
      'description': '庄严神圣的教堂场景',
      'keywords': ['church', 'sacred', 'solemn', 'religious'],
    },
    'garden': {
      'name': '花园',
      'description': '美丽的花园场景',
      'keywords': ['garden', 'flowers', 'nature', 'outdoor'],
    },
    'beach': {
      'name': '海滩',
      'description': '浪漫的海滩场景',
      'keywords': ['beach', 'ocean', 'sunset', 'romantic'],
    },
    'castle': {
      'name': '城堡',
      'description': '童话般的城堡场景',
      'keywords': ['castle', 'fairytale', 'royal', 'majestic'],
    },
    'studio': {
      'name': '摄影棚',
      'description': '专业的室内摄影棚',
      'keywords': ['studio', 'professional', 'controlled', 'lighting'],
    },
  };
  
  /// 获取完整的API URL
  static String getApiUrl(String endpoint) {
    return '$baseUrl$endpoint';
  }
  
  /// 获取请求头
  static Map<String, String> getHeaders() {
    return {
      'Authorization': 'Bearer $apiKey',
      'Content-Type': 'application/json',
    };
  }
  
  /// 验证API密钥是否已设置
  static bool get isApiKeyValid {
    return apiKey.isNotEmpty && apiKey != 'YOUR_API_KEY';
  }
  
  /// 获取风格配置
  static Map<String, dynamic>? getStyleConfig(String styleKey) {
    return weddingStyles[styleKey];
  }
  
  /// 获取场景配置
  static Map<String, dynamic>? getSceneConfig(String sceneKey) {
    return weddingScenes[sceneKey];
  }
  
  /// 验证图片文件大小
  static bool isValidImageSize(int fileSize) {
    return fileSize <= maxSingleImageSize;
  }
  
  /// 验证图片格式
  static bool isValidImageFormat(String extension) {
    return supportedImageFormats.contains(extension.toLowerCase());
  }
  
  /// 验证图片数量
  static bool isValidImageCount(int count) {
    return count > 0 && count <= maxImageCount;
  }
  
  /// 获取调试信息
  static Map<String, dynamic> getDebugInfo() {
    return {
      'baseUrl': baseUrl,
      'apiKeySet': isApiKeyValid,
      'defaultChatModel': defaultChatModel,
      'defaultImageModel': defaultImageModel,
      'maxImageSize': maxSingleImageSize,
      'maxImageCount': maxImageCount,
      'supportedFormats': supportedImageFormats,
    };
  }
}
