import 'dart:convert';
import 'dart:io';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import '../config/ai_config.dart';

/// AI图片生成服务
class AIGenerationService {
  final Dio _dio;

  AIGenerationService() : _dio = Dio() {
    _dio.options.headers = AIConfig.getHeaders();
    _dio.options.connectTimeout = AIConfig.connectTimeout;
    _dio.options.receiveTimeout = AIConfig.receiveTimeout;
    _dio.options.sendTimeout = AIConfig.sendTimeout;

    // 添加请求拦截器用于日志记录
    if (kDebugMode) {
      _dio.interceptors.add(LogInterceptor(
        requestBody: true,
        responseBody: true,
        logPrint: (obj) => debugPrint(obj.toString()),
      ));
    }
  }

  /// 分析图片并生成婚纱照描述
  Future<String> analyzeImageForWeddingPhoto(File imageFile) async {
    try {
      // 验证文件是否存在
      if (!await imageFile.exists()) {
        throw Exception('图片文件不存在');
      }

      // 检查文件大小
      final fileSize = await imageFile.length();
      if (!AIConfig.isValidImageSize(fileSize)) {
        throw Exception('图片文件过大，请选择小于${AIConfig.maxSingleImageSize ~/ (1024 * 1024)}MB的图片');
      }

      // 将图片转换为base64
      final bytes = await imageFile.readAsBytes();
      final base64Image = base64Encode(bytes);

      final prompt = _buildWeddingPhotoPrompt();

      final requestData = {
        'model': AIConfig.defaultChatModel,
        'messages': [
          {
            'role': 'user',
            'content': [
              {
                'type': 'text',
                'text': prompt,
              },
              {
                'type': 'image_url',
                'image_url': {
                  'url': 'data:image/jpeg;base64,$base64Image',
                  'detail': 'high'
                }
              }
            ]
          }
        ],
        'max_tokens': AIConfig.maxAnalysisTokens,
        'temperature': 0.7,
      };

      debugPrint('发送图片分析请求，文件大小: $fileSize 字节');

      final response = await _dio.post(
        AIConfig.getApiUrl('/chat/completions'),
        data: requestData,
      );

      if (response.statusCode == 200) {
        final data = response.data;
        if (data['choices'] != null && data['choices'].isNotEmpty) {
          final content = data['choices'][0]['message']['content'];
          debugPrint('图片分析完成，返回内容长度: ${content.length}');
          return content as String;
        } else {
          throw Exception('API返回数据格式错误');
        }
      } else {
        final errorMessage = response.data?['error']?['message'] ?? '未知错误';
        throw Exception('API调用失败: ${response.statusCode} - $errorMessage');
      }
    } on DioException catch (e) {
      debugPrint('网络请求错误: ${e.message}');
      if (e.response != null) {
        final errorData = e.response!.data;
        final errorMessage = errorData?['error']?['message'] ?? '网络请求失败';
        throw Exception('网络错误: $errorMessage');
      } else {
        throw Exception('网络连接失败，请检查网络设置');
      }
    } catch (e) {
      debugPrint('AI分析错误: $e');
      throw Exception('图片分析失败: $e');
    }
  }

  /// 分析多张图片并生成综合婚纱照描述
  Future<String> analyzeMultipleImagesForWeddingPhoto(List<File> imageFiles) async {
    try {
      if (imageFiles.isEmpty) {
        throw Exception('请至少选择一张图片');
      }

      if (imageFiles.length > 5) {
        throw Exception('最多只能同时分析5张图片');
      }

      List<Map<String, dynamic>> imageContents = [];
      int totalSize = 0;

      // 处理所有图片
      for (int i = 0; i < imageFiles.length; i++) {
        final file = imageFiles[i];

        // 验证文件是否存在
        if (!await file.exists()) {
          throw Exception('第${i + 1}张图片文件不存在');
        }

        // 检查文件大小
        final fileSize = await file.length();
        totalSize += fileSize;

        if (fileSize > 10 * 1024 * 1024) {
          throw Exception('第${i + 1}张图片过大，请选择小于10MB的图片');
        }

        final bytes = await file.readAsBytes();
        final base64Image = base64Encode(bytes);

        imageContents.add({
          'type': 'image_url',
          'image_url': {
            'url': 'data:image/jpeg;base64,$base64Image',
            'detail': 'high'
          }
        });
      }

      // 检查总文件大小
      if (totalSize > 50 * 1024 * 1024) {
        throw Exception('图片总大小超过50MB，请减少图片数量或压缩图片');
      }

      final prompt = _buildMultipleImagesWeddingPrompt(imageFiles.length);

      final requestData = {
        'model': 'gpt-4o',
        'messages': [
          {
            'role': 'user',
            'content': [
              {
                'type': 'text',
                'text': prompt,
              },
              ...imageContents,
            ]
          }
        ],
        'max_tokens': 1200,
        'temperature': 0.7,
      };

      debugPrint('发送多图片分析请求，图片数量: ${imageFiles.length}，总大小: $totalSize 字节');

      final response = await _dio.post(
        AIConfig.getApiUrl('/chat/completions'),
        data: requestData,
      );

      if (response.statusCode == 200) {
        final data = response.data;
        if (data['choices'] != null && data['choices'].isNotEmpty) {
          final content = data['choices'][0]['message']['content'];
          debugPrint('多图片分析完成，返回内容长度: ${content.length}');
          return content as String;
        } else {
          throw Exception('API返回数据格式错误');
        }
      } else {
        final errorMessage = response.data?['error']?['message'] ?? '未知错误';
        throw Exception('API调用失败: ${response.statusCode} - $errorMessage');
      }
    } on DioException catch (e) {
      debugPrint('网络请求错误: ${e.message}');
      if (e.response != null) {
        final errorData = e.response!.data;
        final errorMessage = errorData?['error']?['message'] ?? '网络请求失败';
        throw Exception('网络错误: $errorMessage');
      } else {
        throw Exception('网络连接失败，请检查网络设置');
      }
    } catch (e) {
      debugPrint('AI分析错误: $e');
      throw Exception('图片分析失败: $e');
    }
  }

  /// 使用DALL-E生成婚纱照
  Future<String> generateWeddingPhotoWithDALLE(String description, {
    String style = 'vivid',
    String size = '1024x1024',
    String quality = 'hd',
  }) async {
    try {
      final enhancedPrompt = _enhancePromptForDALLE(description);

      final requestData = {
        'model': 'dall-e-3',
        'prompt': enhancedPrompt,
        'n': 1,
        'size': size,
        'quality': quality,
        'style': style,
        'response_format': 'url',
      };

      debugPrint('发送DALL-E请求: ${requestData['prompt']}');

      final response = await _dio.post(
        AIConfig.getApiUrl('/images/generations'),
        data: requestData,
      );

      if (response.statusCode == 200) {
        final data = response.data;
        if (data['data'] != null && data['data'].isNotEmpty) {
          final imageUrl = data['data'][0]['url'];
          debugPrint('DALL-E生成成功: $imageUrl');
          return imageUrl as String;
        } else {
          throw Exception('API返回数据格式错误');
        }
      } else {
        final errorMessage = response.data?['error']?['message'] ?? '未知错误';
        throw Exception('DALL-E生成失败: ${response.statusCode} - $errorMessage');
      }
    } on DioException catch (e) {
      debugPrint('网络请求错误: ${e.message}');
      if (e.response != null) {
        final errorData = e.response!.data;
        final errorMessage = errorData?['error']?['message'] ?? '网络请求失败';
        throw Exception('网络错误: $errorMessage');
      } else {
        throw Exception('网络连接失败，请检查网络设置');
      }
    } catch (e) {
      debugPrint('DALL-E生成错误: $e');
      throw Exception('图片生成失败: $e');
    }
  }

  /// 生成多种风格的婚纱照
  Future<List<String>> generateMultipleStyleWeddingPhotos(
    String description, {
    List<String> styles = const ['vivid', 'natural'],
    String size = '1024x1024',
    String quality = 'hd',
  }) async {
    final results = <String>[];

    for (final style in styles) {
      try {
        final imageUrl = await generateWeddingPhotoWithDALLE(
          description,
          style: style,
          size: size,
          quality: quality,
        );
        results.add(imageUrl);

        // 添加延迟避免API限制
        await Future.delayed(const Duration(seconds: 2));
      } catch (e) {
        debugPrint('风格 $style 生成失败: $e');
        // 继续生成其他风格，不中断整个流程
      }
    }

    if (results.isEmpty) {
      throw Exception('所有风格的图片生成都失败了');
    }

    return results;
  }

  /// 构建单张图片婚纱照分析提示词
  String _buildWeddingPhotoPrompt() {
    return '''
作为专业的婚纱摄影AI分析师，请仔细分析这张照片中的人物特征，并为生成高质量婚纱照提供详细的专业描述。

**核心分析要求**：

1. **精准人物特征分析**：
   - 面部轮廓：脸型（圆脸/方脸/瓜子脸/长脸），下颌线条，颧骨高度
   - 五官细节：眼型（单眼皮/双眼皮/内双），眼距，鼻型，唇形，眉型
   - 肤色分析：冷调/暖调，肤质状态，适合的妆容色系
   - 发型特征：发质（直发/卷发），发量，发色，脸型适配度
   - 身材比例：肩宽，腰臀比，身高比例，体型特点

2. **专业婚纱照风格定制**：
   - **经典优雅风**：适合的婚纱款式（A字裙/鱼尾裙/公主裙/修身款）
   - **现代时尚风**：简约线条，都市背景，时尚元素
   - **浪漫梦幻风**：仙女裙，花园场景，柔美光线
   - **复古宫廷风**：华丽装饰，古典建筑，奢华配饰
   - **自然清新风**：户外场景，自然光线，简约妆容

3. **拍摄技术参数建议**：
   - 最佳拍摄角度：正面/侧面/45度角
   - 灯光布置：主光源方向，补光需求，氛围营造
   - 构图建议：全身/半身/特写，景深控制
   - 色彩搭配：主色调，辅助色，对比色运用

4. **细节优化建议**：
   - 妆容重点：突出优势五官，修饰不足
   - 配饰选择：头饰，项链，耳环，手套等
   - 姿态指导：手部摆放，身体角度，表情管理
   - 后期处理：皮肤质感，色彩调整，氛围渲染

**输出要求**：
请提供一个完整的、专业的、适合AI图片生成的中文描述，包含具体的视觉元素、技术参数和艺术风格，确保生成的婚纱照达到专业摄影工作室的品质标准。重点突出浪漫、优雅、梦幻的婚纱照特质。
''';
  }

  /// 构建多张图片婚纱照分析提示词
  String _buildMultipleImagesWeddingPrompt(int imageCount) {
    return '''
作为顶级婚纱摄影AI专家，请综合分析这$imageCount张照片中的人物特征，为创作世界级婚纱照提供全方位的专业指导。

**深度综合分析框架**：

1. **多角度人物特征整合**：
   - **面部特征一致性分析**：对比不同角度的面部轮廓，确定最佳展现角度
   - **表情神态研究**：分析自然表情状态，捕捉最具魅力的神态特征
   - **身材比例优化**：综合多张照片的身材信息，确定最佳拍摄角度和姿态
   - **气质风格定位**：从整体形象中提取独特的个人气质标签

2. **顶级婚纱照创意方案矩阵**：
   - **主题一：经典永恒**
     * 场景：欧式教堂/古典庄园/大理石宫殿
     * 婚纱：经典A字裙，蕾丝细节，长拖尾
     * 氛围：庄重优雅，永恒经典

   - **主题二：现代奢华**
     * 场景：现代艺术馆/顶层天台/豪华酒店
     * 婚纱：简约修身，丝绸质感，几何线条
     * 氛围：时尚前卫，都市精英

   - **主题三：浪漫梦幻**
     * 场景：花海/湖边/云端/星空
     * 婚纱：蓬松纱裙，花朵装饰，仙女风
     * 氛围：梦幻唯美，童话般浪漫

   - **主题四：自然清新**
     * 场景：森林/海滩/草原/山谷
     * 婚纱：简约设计，自然材质，飘逸感
     * 氛围：清新自然，回归本真

3. **专业技术参数优化**：
   - **光影设计**：黄金时段拍摄，柔光布置，轮廓光运用
   - **构图美学**：黄金分割，对称平衡，景深层次
   - **色彩科学**：冷暖对比，饱和度控制，色彩心理学应用
   - **后期标准**：好莱坞级调色，细节精修，质感提升

4. **个性化定制要素**：
   - **妆容设计**：根据肤色和五官特点定制专属妆容
   - **配饰搭配**：头饰、珠宝、手套等细节配饰的精准选择
   - **姿态指导**：基于身材优势设计最美姿态和手部动作
   - **表情管理**：捕捉最自然、最动人的表情瞬间

**最终输出标准**：
请提供一个国际顶级婚纱摄影工作室水准的完整创作方案，包含具体的视觉描述、技术参数、艺术风格和执行细节。确保AI生成的婚纱照达到杂志封面级别的专业品质，完美展现新娘的独特魅力和婚纱照的浪漫本质。
''';
  }

  /// 增强DALL-E生成提示词
  String _enhancePromptForDALLE(String originalDescription) {
    final enhancedPrompt = '''
Ultra-high quality professional wedding photography masterpiece, stunning bride portrait,
exquisite wedding dress with intricate details, perfect studio lighting setup,
romantic and dreamy atmosphere, soft bokeh background, professional photographer composition,
wedding magazine cover quality, 8K ultra-resolution, photorealistic rendering,
cinematic lighting, elegant pose, flawless skin texture, beautiful facial features,
luxury wedding dress fabric details, romantic mood lighting, artistic composition,
professional retouching quality, fashion photography style.

Detailed requirements based on AI analysis:
$originalDescription

Technical specifications:
- Camera: Professional DSLR with 85mm portrait lens
- Lighting: Soft key light with fill light and rim light setup
- Composition: Rule of thirds, perfect depth of field
- Style: High-end fashion wedding photography
- Quality: Magazine cover standard, commercial photography grade
- Mood: Romantic, elegant, timeless, dreamy
- Details: Sharp focus on eyes, soft skin texture, fabric details
- Background: Beautifully blurred, complementary colors
- Post-processing: Professional color grading, skin retouching

Style keywords: elegant, romantic, dreamy, professional, high-fashion, luxury,
timeless, sophisticated, artistic, cinematic, magazine-quality, wedding perfection.
''';

    return enhancedPrompt;
  }

  /// 生成婚纱照的完整流程
  Future<GenerationResult> generateWeddingPhotos(
    List<File> images, {
    String style = 'vivid',
    String size = '1024x1024',
    String quality = 'hd',
    bool generateMultipleStyles = false,
  }) async {
    try {
      debugPrint('开始婚纱照生成流程，图片数量: ${images.length}');

      // 步骤1: 分析图片
      String analysis;
      if (images.length == 1) {
        debugPrint('分析单张图片...');
        analysis = await analyzeImageForWeddingPhoto(images.first);
      } else {
        debugPrint('分析多张图片...');
        analysis = await analyzeMultipleImagesForWeddingPhoto(images);
      }

      debugPrint('图片分析完成');

      // 步骤2: 生成图片
      String generatedImageUrl;
      List<String> additionalUrls = [];

      if (generateMultipleStyles) {
        debugPrint('生成多种风格的婚纱照...');
        final urls = await generateMultipleStyleWeddingPhotos(
          analysis,
          styles: ['vivid', 'natural'],
          size: size,
          quality: quality,
        );
        generatedImageUrl = urls.first;
        if (urls.length > 1) {
          additionalUrls = urls.sublist(1);
        }
      } else {
        debugPrint('生成单一风格的婚纱照...');
        generatedImageUrl = await generateWeddingPhotoWithDALLE(
          analysis,
          style: style,
          size: size,
          quality: quality,
        );
      }

      debugPrint('婚纱照生成完成');

      return GenerationResult(
        success: true,
        analysis: analysis,
        generatedImageUrl: generatedImageUrl,
        additionalImageUrls: additionalUrls,
        message: '婚纱照生成成功！',
        generationTime: DateTime.now(),
        style: style,
        size: size,
        quality: quality,
      );
    } catch (e) {
      debugPrint('婚纱照生成失败: $e');
      return GenerationResult(
        success: false,
        analysis: '',
        generatedImageUrl: '',
        additionalImageUrls: [],
        message: '生成失败: $e',
        generationTime: DateTime.now(),
        style: style,
        size: size,
        quality: quality,
      );
    }
  }

  /// 快速生成婚纱照（使用预设参数）
  Future<GenerationResult> quickGenerateWeddingPhoto(File image) async {
    return generateWeddingPhotos(
      [image],
      style: 'vivid',
      size: '1024x1024',
      quality: 'hd',
    );
  }

  /// 高质量生成婚纱照（多风格）
  Future<GenerationResult> premiumGenerateWeddingPhotos(List<File> images) async {
    return generateWeddingPhotos(
      images,
      style: 'vivid',
      size: '1024x1024',
      quality: 'hd',
      generateMultipleStyles: true,
    );
  }
}

/// 生成结果数据类
class GenerationResult {
  final bool success;
  final String analysis;
  final String generatedImageUrl;
  final List<String> additionalImageUrls;
  final String message;
  final DateTime generationTime;
  final String style;
  final String size;
  final String quality;

  GenerationResult({
    required this.success,
    required this.analysis,
    required this.generatedImageUrl,
    required this.message,
    this.additionalImageUrls = const [],
    DateTime? generationTime,
    this.style = 'vivid',
    this.size = '1024x1024',
    this.quality = 'hd',
  }) : generationTime = generationTime ?? DateTime.now();

  /// 获取所有生成的图片URL
  List<String> get allImageUrls {
    if (generatedImageUrl.isEmpty) return additionalImageUrls;
    return [generatedImageUrl, ...additionalImageUrls];
  }

  /// 是否有多张生成的图片
  bool get hasMultipleImages => additionalImageUrls.isNotEmpty;

  /// 生成的图片总数
  int get imageCount => allImageUrls.length;

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'analysis': analysis,
      'generatedImageUrl': generatedImageUrl,
      'additionalImageUrls': additionalImageUrls,
      'message': message,
      'generationTime': generationTime.toIso8601String(),
      'style': style,
      'size': size,
      'quality': quality,
    };
  }

  /// 从JSON创建实例
  factory GenerationResult.fromJson(Map<String, dynamic> json) {
    return GenerationResult(
      success: json['success'] ?? false,
      analysis: json['analysis'] ?? '',
      generatedImageUrl: json['generatedImageUrl'] ?? '',
      additionalImageUrls: List<String>.from(json['additionalImageUrls'] ?? []),
      message: json['message'] ?? '',
      generationTime: json['generationTime'] != null
          ? DateTime.parse(json['generationTime'])
          : DateTime.now(),
      style: json['style'] ?? 'vivid',
      size: json['size'] ?? '1024x1024',
      quality: json['quality'] ?? 'hd',
    );
  }
}